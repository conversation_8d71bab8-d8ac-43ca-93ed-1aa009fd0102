package response

import "yotracker/internal/models"

type UserResponse struct {
	Id              uint          `json:"id"`
	ClientId        *uint         `json:"client_id"`
	Name            string        `json:"name"`
	Email           string        `json:"email"`
	Gender          *string       `json:"gender"`
	LastLoginDate   string        `json:"last_login_date"`
	Status          *string       `json:"status"`
	TelegramUserId  *string       `json:"telegramUserId"`
	SlackWebhookUrl *string       `json:"slack_webhook_url"`
	Description     *string       `json:"description"`
	CreatedAt       string        `json:"created_at"`
	UserType        *string       `json:"user_type"`
	Client          models.Client `json:"client"`
}
